import pandas as pd
import re

# 读取文件内容
with open('cooking_vote_topics.md', 'r', encoding='utf-8') as f:
    content = f.read()

# 解析数据
data = []

# 按行处理
lines = content.split('\n')
current_category = ''
current_topic = {}

for line in lines:
    line = line.strip()

    # 匹配类别标题（## 类别名类话题）
    category_match = re.match(r'## (.+)类话题', line)
    if category_match:
        current_category = category_match.group(1)
        print(f"找到类别: {current_category}")
        continue

    # 匹配话题标题（### 数字. 话题内容）
    topic_match = re.match(r'### \d+\. (.+)', line)
    if topic_match:
        # 保存上一个话题
        if current_topic and all(v for v in current_topic.values() if v):
            data.append(current_topic)
            print(f"保存话题: {current_topic['话题']}")

        # 开始新话题
        current_topic = {
            '话题类别': current_category,
            '话题': topic_match.group(1),
            '选项A': '',
            '选项B': '',
            '标签': ''
        }
        continue

    # 匹配选项A
    if line.startswith('**选项A：**'):
        if current_topic:
            current_topic['选项A'] = line.replace('**选项A：**', '').strip()
        continue

    # 匹配选项B
    if line.startswith('**选项B：**'):
        if current_topic:
            current_topic['选项B'] = line.replace('**选项B：**', '').strip()
        continue

    # 匹配标签
    if line.startswith('`#'):
        if current_topic:
            # 提取标签，去掉反引号和#号
            tags = line.replace('`', '').replace('#', '').strip()
            current_topic['标签'] = tags
        continue

# 保存最后一个话题
if current_topic and all(v for v in current_topic.values() if v):
    data.append(current_topic)
    print(f"保存最后话题: {current_topic['话题']}")

# 创建DataFrame
print(f"\n总共解析出 {len(data)} 个话题")
if data:
    df = pd.DataFrame(data)

    # 保存为xlsx文件
    df.to_excel('cooking_vote_topics.xlsx', index=False, engine='openpyxl')

    print(f'成功转换 {len(data)} 个话题到 cooking_vote_topics.xlsx')
    print('前5行数据预览：')
    print(df.head())
    print('\n数据统计：')
    print(df['话题类别'].value_counts())
else:
    print("没有解析到任何数据")
